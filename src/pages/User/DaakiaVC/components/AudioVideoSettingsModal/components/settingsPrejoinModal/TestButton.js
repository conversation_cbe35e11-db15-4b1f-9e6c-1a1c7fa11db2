import React from 'react';

/**
 * TestButton Component
 * Reusable test button for microphone and speaker testing
 * @param {function} onClick - Click handler function
 * @param {boolean} isActive - Whether the test is currently active
 * @param {string} activeText - Text to show when active
 * @param {string} inactiveText - Text to show when inactive
 * @param {boolean} disabled - Whether the button is disabled
 */
function TestButton({
  onClick,
  isActive = false,
  activeText = 'Stop Test',
  inactiveText = 'Test',
  disabled = false
}) {
  return (
    <div
      onClick={disabled ? undefined : onClick}
      style={{
        height: '32px', // Smaller height
        padding: '0 12px', // Smaller padding
        border: 'none',
        borderRadius: '6px',
        background: isActive ? '#3B60E4' : '#D2D2D2',
        color: isActive ? '#fff' : '#555454',
        fontSize: '12px', // Smaller font size
        fontWeight: '500',
        minWidth: '80px', // Smaller minimum width
        cursor: disabled ? 'not-allowed' : 'pointer',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        opacity: disabled ? 0.5 : 1,
        userSelect: 'none',
        transition: 'all 0.3s ease' // Add smooth transition
      }}
    >
      {isActive ? activeText : inactiveText}
    </div>
  );
}

export default TestButton;
